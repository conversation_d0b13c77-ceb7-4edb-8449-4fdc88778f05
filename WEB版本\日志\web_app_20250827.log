2025-08-27 08:56:54,644 - WARNING -  * <PERSON>bu<PERSON> is active!
2025-08-27 08:56:54,649 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 08:56:56,547 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-27 08:56:59,316 - WARNING -  * <PERSON>bu<PERSON> is active!
2025-08-27 08:56:59,322 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 08:57:43,817 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 08:57:43,817 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 08:57:43,818 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 08:57:45,129 - WARNING -  * <PERSON>bu<PERSON> is active!
2025-08-27 08:57:45,135 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 08:57:46,869 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:46] "GET / HTTP/1.1" 200 -
2025-08-27 08:57:47,063 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:47,069 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:47,683 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 08:57:47,684 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-sK HTTP/1.1" 200 -
2025-08-27 08:57:47,685 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 08:57:47,742 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "POST /socket.io/?EIO=4&transport=polling&t=PZf1-tN&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:47,744 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-tO&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:47,796 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-uI&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:47,811 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-uT&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:48,896 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:48] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 08:57:48,941 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:48] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:48,943 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:48] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:49,075 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_CD HTTP/1.1" 200 -
2025-08-27 08:57:49,076 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_CE HTTP/1.1" 200 -
2025-08-27 08:57:49,079 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 08:57:49,140 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_DE&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,140 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DF&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,141 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_DF.0&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:49,141 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=websocket&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:49,142 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DF.1&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:49,152 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DT&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,157 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DV&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:49,175 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Dq&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,180 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Ds&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:50,311 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 08:57:50,318 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=websocket&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:50,318 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=websocket&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:50,344 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:50,347 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:50,467 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Xy HTTP/1.1" 200 -
2025-08-27 08:57:50,468 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 08:57:50,475 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Xz HTTP/1.1" 200 -
2025-08-27 08:57:50,504 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_YZ&sid=c5O2B3Fg6ld0sXKFAAAG HTTP/1.1" 200 -
2025-08-27 08:57:50,505 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Ya&sid=c5O2B3Fg6ld0sXKFAAAG HTTP/1.1" 200 -
2025-08-27 08:57:50,513 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_Yb&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,513 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Yb.0&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,529 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Y_&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,541 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_ZA&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,747 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 08:57:50,797 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:50,798 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:51,027 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_gk HTTP/1.1" 200 -
2025-08-27 08:57:51,054 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 08:57:51,064 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_hL&sid=KBfu-E8SkQguB168AAAK HTTP/1.1" 200 -
2025-08-27 08:57:51,065 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=websocket&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:51,066 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_hL.0&sid=KBfu-E8SkQguB168AAAK HTTP/1.1" 200 -
2025-08-27 08:57:51,066 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=websocket&sid=c5O2B3Fg6ld0sXKFAAAG HTTP/1.1" 200 -
2025-08-27 08:57:51,124 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_iF&sid=KBfu-E8SkQguB168AAAK HTTP/1.1" 200 -
2025-08-27 09:02:00,468 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:02:00,469 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:02:01,465 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:02:02,637 - WARNING -  * Debugger is active!
2025-08-27 09:02:02,640 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:02:03,222 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:03] "GET /socket.io/?EIO=4&transport=polling&t=PZf2zFJ HTTP/1.1" 200 -
2025-08-27 09:02:03,227 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:03] "POST /socket.io/?EIO=4&transport=polling&t=PZf2zFO&sid=BCWHcSZOUUOhuzKtAAAA HTTP/1.1" 200 -
2025-08-27 09:02:03,228 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:03] "GET /socket.io/?EIO=4&transport=polling&t=PZf2zFO.0&sid=BCWHcSZOUUOhuzKtAAAA HTTP/1.1" 200 -
2025-08-27 09:02:22,701 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:02:22,701 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:02:22,702 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:02:23,888 - WARNING -  * Debugger is active!
2025-08-27 09:02:23,893 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:02:50,874 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:50] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 09:02:51,039 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:02:51,053 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:02:51,579 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf392v HTTP/1.1" 200 -
2025-08-27 09:02:51,582 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 09:02:51,619 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "POST /socket.io/?EIO=4&transport=polling&t=PZf393V&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:02:51,620 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf393W&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:02:51,888 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf3971&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:02:52,666 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:52] "GET /socket.io/?EIO=4&transport=websocket&sid=BCWHcSZOUUOhuzKtAAAA HTTP/1.1" 200 -
2025-08-27 09:04:09,807 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:09] "GET /新上架商品 HTTP/1.1" 200 -
2025-08-27 09:04:09,836 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:09] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:04:09,848 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:09] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:04:10,433 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /socket.io/?EIO=4&transport=polling&t=PZf3SIz HTTP/1.1" 200 -
2025-08-27 09:04:10,439 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /api/获取新上架商品 HTTP/1.1" 200 -
2025-08-27 09:04:10,488 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "POST /socket.io/?EIO=4&transport=polling&t=PZf3SJr&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:10,489 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /socket.io/?EIO=4&transport=polling&t=PZf3SJr.0&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:10,766 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /socket.io/?EIO=4&transport=polling&t=PZf3SNE&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:23,620 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:04:23,620 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:04:23,620 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:04:24,912 - WARNING -  * Debugger is active!
2025-08-27 09:04:24,916 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:04:37,212 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:37] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 09:04:37,279 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:37] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:04:37,285 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:37] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:04:38,022 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:38] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 09:04:38,029 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:38] "GET /socket.io/?EIO=4&transport=polling&t=PZf3Z1B HTTP/1.1" 200 -
2025-08-27 09:04:38,119 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:38] "POST /socket.io/?EIO=4&transport=polling&t=PZf3Z39&sid=GLWSgecFYeYlB-Y3AAAG HTTP/1.1" 200 -
2025-08-27 09:04:38,120 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:38] "GET /socket.io/?EIO=4&transport=polling&t=PZf3Z3A&sid=GLWSgecFYeYlB-Y3AAAG HTTP/1.1" 200 -
2025-08-27 09:04:38,456 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:38] "GET /socket.io/?EIO=4&transport=polling&t=PZf3Z7d&sid=GLWSgecFYeYlB-Y3AAAG HTTP/1.1" 200 -
2025-08-27 09:04:44,648 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:44] "GET /socket.io/?EIO=4&transport=websocket&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:04:44,899 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:44] "GET /socket.io/?EIO=4&transport=websocket&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:46,957 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:46] "GET /AI提纯 HTTP/1.1" 200 -
2025-08-27 09:04:47,016 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:04:47,017 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:04:47,149 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf3bGf HTTP/1.1" 200 -
2025-08-27 09:04:47,152 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:04:47,193 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "POST /socket.io/?EIO=4&transport=polling&t=PZf3bHL&sid=-2Lkz0ktHoWKHpOrAAAI HTTP/1.1" 200 -
2025-08-27 09:04:47,195 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf3bHL.0&sid=-2Lkz0ktHoWKHpOrAAAI HTTP/1.1" 200 -
2025-08-27 09:04:47,212 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /socket.io/?EIO=4&transport=websocket&sid=GLWSgecFYeYlB-Y3AAAG HTTP/1.1" 200 -
2025-08-27 09:04:47,226 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf3bHr&sid=-2Lkz0ktHoWKHpOrAAAI HTTP/1.1" 200 -
2025-08-27 09:04:47,245 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf3bI3&sid=-2Lkz0ktHoWKHpOrAAAI HTTP/1.1" 200 -
2025-08-27 09:05:11,352 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /新上架商品 HTTP/1.1" 200 -
2025-08-27 09:05:11,389 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:05:11,389 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:05:11,565 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hE7 HTTP/1.1" 200 -
2025-08-27 09:05:11,566 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /api/获取新上架商品 HTTP/1.1" 200 -
2025-08-27 09:05:11,608 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "POST /socket.io/?EIO=4&transport=polling&t=PZf3hEr&sid=Vj4Hk_mQLvjrPd-OAAAK HTTP/1.1" 200 -
2025-08-27 09:05:11,610 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hEs&sid=Vj4Hk_mQLvjrPd-OAAAK HTTP/1.1" 200 -
2025-08-27 09:05:11,613 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /socket.io/?EIO=4&transport=websocket&sid=-2Lkz0ktHoWKHpOrAAAI HTTP/1.1" 200 -
2025-08-27 09:05:11,627 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hF6&sid=Vj4Hk_mQLvjrPd-OAAAK HTTP/1.1" 200 -
2025-08-27 09:05:11,698 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hGF&sid=Vj4Hk_mQLvjrPd-OAAAK HTTP/1.1" 200 -
2025-08-27 09:05:12,063 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "GET /系统设置 HTTP/1.1" 200 -
2025-08-27 09:05:12,136 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:05:12,138 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:05:12,297 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hPV HTTP/1.1" 200 -
2025-08-27 09:05:12,347 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "POST /socket.io/?EIO=4&transport=polling&t=PZf3hQO&sid=Otnx8p1vOkObCcliAAAM HTTP/1.1" 200 -
2025-08-27 09:05:12,349 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "GET /socket.io/?EIO=4&transport=websocket&sid=Vj4Hk_mQLvjrPd-OAAAK HTTP/1.1" 200 -
2025-08-27 09:05:12,349 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hQO.0&sid=Otnx8p1vOkObCcliAAAM HTTP/1.1" 200 -
2025-08-27 09:05:12,364 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hQe&sid=Otnx8p1vOkObCcliAAAM HTTP/1.1" 200 -
2025-08-27 09:05:33,075 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:05:33,113 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:05:33,118 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:05:33,342 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mYQ HTTP/1.1" 200 -
2025-08-27 09:05:33,343 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mYR HTTP/1.1" 200 -
2025-08-27 09:05:33,345 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:05:33,375 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "POST /socket.io/?EIO=4&transport=polling&t=PZf3mYv&sid=5R-eW8pGkKWUQfaJAAAO HTTP/1.1" 200 -
2025-08-27 09:05:33,377 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=websocket&sid=Otnx8p1vOkObCcliAAAM HTTP/1.1" 200 -
2025-08-27 09:05:33,378 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mYw&sid=5R-eW8pGkKWUQfaJAAAO HTTP/1.1" 200 -
2025-08-27 09:05:33,380 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "POST /socket.io/?EIO=4&transport=polling&t=PZf3mYw.0&sid=YHEkDJNKVvcX2gEzAAAP HTTP/1.1" 200 -
2025-08-27 09:05:33,381 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mYx&sid=YHEkDJNKVvcX2gEzAAAP HTTP/1.1" 200 -
2025-08-27 09:05:33,410 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mZO&sid=5R-eW8pGkKWUQfaJAAAO HTTP/1.1" 200 -
2025-08-27 09:05:33,411 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mZP&sid=YHEkDJNKVvcX2gEzAAAP HTTP/1.1" 200 -
2025-08-27 09:05:33,447 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3ma4&sid=5R-eW8pGkKWUQfaJAAAO HTTP/1.1" 200 -
2025-08-27 09:05:33,447 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3ma5&sid=YHEkDJNKVvcX2gEzAAAP HTTP/1.1" 200 -
2025-08-27 09:05:33,930 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET / HTTP/1.1" 200 -
2025-08-27 09:05:33,956 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:05:33,959 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:05:34,075 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /socket.io/?EIO=4&transport=websocket&sid=5R-eW8pGkKWUQfaJAAAO HTTP/1.1" 200 -
2025-08-27 09:05:34,075 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /socket.io/?EIO=4&transport=websocket&sid=YHEkDJNKVvcX2gEzAAAP HTTP/1.1" 200 -
2025-08-27 09:05:34,085 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mjr HTTP/1.1" 200 -
2025-08-27 09:05:34,087 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:05:34,088 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:05:34,135 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "POST /socket.io/?EIO=4&transport=polling&t=PZf3mkj&sid=mUeG22LsSrH_Lek1AAAS HTTP/1.1" 200 -
2025-08-27 09:05:34,137 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mkk&sid=mUeG22LsSrH_Lek1AAAS HTTP/1.1" 200 -
2025-08-27 09:05:34,153 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /socket.io/?EIO=4&transport=polling&t=PZf3ml7&sid=mUeG22LsSrH_Lek1AAAS HTTP/1.1" 200 -
2025-08-27 09:05:48,193 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:05:48,232 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:05:48,233 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:05:48,382 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qDE HTTP/1.1" 200 -
2025-08-27 09:05:48,387 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qDF HTTP/1.1" 200 -
2025-08-27 09:05:48,388 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:05:48,394 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=websocket&sid=mUeG22LsSrH_Lek1AAAS HTTP/1.1" 200 -
2025-08-27 09:05:48,444 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "POST /socket.io/?EIO=4&transport=polling&t=PZf3qEJ&sid=sDYJB_ax1O_dK213AAAU HTTP/1.1" 200 -
2025-08-27 09:05:48,445 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qEK&sid=sDYJB_ax1O_dK213AAAU HTTP/1.1" 200 -
2025-08-27 09:05:48,446 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "POST /socket.io/?EIO=4&transport=polling&t=PZf3qEL&sid=m1XilxyBNRkc-SraAAAV HTTP/1.1" 200 -
2025-08-27 09:05:48,446 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qEM&sid=m1XilxyBNRkc-SraAAAV HTTP/1.1" 200 -
2025-08-27 09:05:48,473 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qEr&sid=sDYJB_ax1O_dK213AAAU HTTP/1.1" 200 -
2025-08-27 09:05:48,474 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qEs&sid=m1XilxyBNRkc-SraAAAV HTTP/1.1" 200 -
2025-08-27 09:05:48,512 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qFT&sid=m1XilxyBNRkc-SraAAAV HTTP/1.1" 200 -
2025-08-27 09:08:25,186 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:08:25,187 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:08:25,187 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:08:26,695 - WARNING -  * Debugger is active!
2025-08-27 09:08:26,700 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:08:40,236 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 09:08:40,340 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /static/js/主脚本.js HTTP/1.1" 200 -
2025-08-27 09:08:40,357 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /static/css/主样式.css HTTP/1.1" 200 -
2025-08-27 09:08:40,820 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /socket.io/?EIO=4&transport=polling&t=PZf4UJn HTTP/1.1" 200 -
2025-08-27 09:08:40,821 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 09:08:40,862 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "POST /socket.io/?EIO=4&transport=polling&t=PZf4UKQ&sid=iwKzftLvuhOxSQNnAAAA HTTP/1.1" 200 -
2025-08-27 09:08:40,865 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /socket.io/?EIO=4&transport=polling&t=PZf4UKR&sid=iwKzftLvuhOxSQNnAAAA HTTP/1.1" 200 -
2025-08-27 09:08:41,118 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:41] "GET /socket.io/?EIO=4&transport=polling&t=PZf4UNl&sid=iwKzftLvuhOxSQNnAAAA HTTP/1.1" 200 -
2025-08-27 09:08:51,184 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "GET /新上架商品 HTTP/1.1" 200 -
2025-08-27 09:08:51,212 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:08:51,223 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:08:51,749 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf4W-Z HTTP/1.1" 200 -
2025-08-27 09:08:51,751 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "GET /api/获取新上架商品 HTTP/1.1" 200 -
2025-08-27 09:08:51,796 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "POST /socket.io/?EIO=4&transport=polling&t=PZf4W_F&sid=24EQ_vMxSC3p36f1AAAC HTTP/1.1" 200 -
2025-08-27 09:08:51,797 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf4W_G&sid=24EQ_vMxSC3p36f1AAAC HTTP/1.1" 200 -
2025-08-27 09:08:51,971 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf4X21&sid=24EQ_vMxSC3p36f1AAAC HTTP/1.1" 200 -
2025-08-27 09:09:14,467 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /AI提纯 HTTP/1.1" 200 -
2025-08-27 09:09:14,619 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:09:14,647 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:09:14,783 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:09:14,787 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /socket.io/?EIO=4&transport=websocket&sid=24EQ_vMxSC3p36f1AAAC HTTP/1.1" 200 -
2025-08-27 09:09:14,792 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /socket.io/?EIO=4&transport=polling&t=PZf4cbD HTTP/1.1" 200 -
2025-08-27 09:09:14,821 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "POST /socket.io/?EIO=4&transport=polling&t=PZf4ccz&sid=yoRXqs6X4ZeOYeGZAAAE HTTP/1.1" 200 -
2025-08-27 09:09:14,888 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /socket.io/?EIO=4&transport=polling&t=PZf4cc-&sid=yoRXqs6X4ZeOYeGZAAAE HTTP/1.1" 200 -
2025-08-27 09:09:14,905 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 09:09:14,994 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:09:15,011 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:09:15,144 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "GET /socket.io/?EIO=4&transport=polling&t=PZf4ci0 HTTP/1.1" 200 -
2025-08-27 09:09:15,148 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 09:09:15,156 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "GET /socket.io/?EIO=4&transport=websocket&sid=yoRXqs6X4ZeOYeGZAAAE HTTP/1.1" 200 -
2025-08-27 09:09:15,177 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "POST /socket.io/?EIO=4&transport=polling&t=PZf4ciR&sid=JAYyQGip9FFt0JiOAAAG HTTP/1.1" 200 -
2025-08-27 09:09:15,197 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "GET /socket.io/?EIO=4&transport=polling&t=PZf4ciS&sid=JAYyQGip9FFt0JiOAAAG HTTP/1.1" 200 -
2025-08-27 09:09:15,479 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "GET /socket.io/?EIO=4&transport=polling&t=PZf4clr&sid=JAYyQGip9FFt0JiOAAAG HTTP/1.1" 200 -
2025-08-27 09:29:52,807 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:29:52,808 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:29:52,808 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:29:53,547 - WARNING -  * Debugger is active!
2025-08-27 09:29:53,551 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:29:53,561 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "GET /socket.io/?EIO=4&transport=polling&t=PZf9KjN HTTP/1.1" 200 -
2025-08-27 09:29:53,562 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "GET /socket.io/?EIO=4&transport=polling&t=PZf9KjN HTTP/1.1" 200 -
2025-08-27 09:29:53,568 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "POST /socket.io/?EIO=4&transport=polling&t=PZf9L2R&sid=M7Uozy6axcJKHj9hAAAA HTTP/1.1" 200 -
2025-08-27 09:29:53,570 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "POST /socket.io/?EIO=4&transport=polling&t=PZf9L2U&sid=QjoASvykpBbNnU-CAAAB HTTP/1.1" 200 -
2025-08-27 09:29:53,571 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "GET /socket.io/?EIO=4&transport=polling&t=PZf9L2T&sid=M7Uozy6axcJKHj9hAAAA HTTP/1.1" 200 -
2025-08-27 09:29:53,572 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "GET /socket.io/?EIO=4&transport=polling&t=PZf9L2V&sid=QjoASvykpBbNnU-CAAAB HTTP/1.1" 200 -
2025-08-27 09:29:53,594 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "GET /socket.io/?EIO=4&transport=polling&t=PZf9L2c&sid=QjoASvykpBbNnU-CAAAB HTTP/1.1" 200 -
2025-08-27 09:31:40,332 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:31:40,333 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:31:40,333 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:31:40,949 - WARNING -  * Debugger is active!
2025-08-27 09:31:40,953 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:31:45,210 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "GET /socket.io/?EIO=4&transport=polling&t=PZf9mIu HTTP/1.1" 200 -
2025-08-27 09:31:45,212 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "GET /socket.io/?EIO=4&transport=polling&t=PZf9mIu HTTP/1.1" 200 -
2025-08-27 09:31:45,216 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "POST /socket.io/?EIO=4&transport=polling&t=PZf9mIy&sid=okBkDYasXBGEBZEHAAAA HTTP/1.1" 200 -
2025-08-27 09:31:45,217 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "GET /socket.io/?EIO=4&transport=polling&t=PZf9mIz&sid=okBkDYasXBGEBZEHAAAA HTTP/1.1" 200 -
2025-08-27 09:31:45,219 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "POST /socket.io/?EIO=4&transport=polling&t=PZf9mI_&sid=pBpHkSmFLmW02XB1AAAB HTTP/1.1" 200 -
2025-08-27 09:31:45,221 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "GET /socket.io/?EIO=4&transport=polling&t=PZf9mJ0&sid=pBpHkSmFLmW02XB1AAAB HTTP/1.1" 200 -
2025-08-27 09:31:45,234 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "GET /socket.io/?EIO=4&transport=polling&t=PZf9mJ6&sid=pBpHkSmFLmW02XB1AAAB HTTP/1.1" 200 -
2025-08-27 09:32:04,700 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:32:04,700 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:32:04,701 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:32:05,342 - WARNING -  * Debugger is active!
2025-08-27 09:32:05,345 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:32:07,230 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:07] "GET /socket.io/?EIO=4&transport=polling&t=PZf9rgn HTTP/1.1" 200 -
2025-08-27 09:32:07,241 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:07] "POST /socket.io/?EIO=4&transport=polling&t=PZf9rh2&sid=BHjx5vJS0_V69uebAAAA HTTP/1.1" 200 -
2025-08-27 09:32:07,243 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:07] "GET /socket.io/?EIO=4&transport=polling&t=PZf9rh4&sid=BHjx5vJS0_V69uebAAAA HTTP/1.1" 200 -
2025-08-27 09:32:08,216 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:08] "GET /socket.io/?EIO=4&transport=polling&t=PZf9rwM HTTP/1.1" 200 -
2025-08-27 09:32:08,221 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:08] "POST /socket.io/?EIO=4&transport=polling&t=PZf9rwQ&sid=iWvRtEdudGUz7d1NAAAC HTTP/1.1" 200 -
2025-08-27 09:32:08,223 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:08] "GET /socket.io/?EIO=4&transport=polling&t=PZf9rwR&sid=iWvRtEdudGUz7d1NAAAC HTTP/1.1" 200 -
2025-08-27 09:33:16,827 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:33:16,828 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:33:16,828 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:33:17,496 - WARNING -  * Debugger is active!
2025-08-27 09:33:17,500 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:33:17,508 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "GET /socket.io/?EIO=4&transport=polling&t=PZfA6mX HTTP/1.1" 200 -
2025-08-27 09:33:17,511 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "GET /socket.io/?EIO=4&transport=polling&t=PZfA6mX HTTP/1.1" 200 -
2025-08-27 09:33:17,517 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "POST /socket.io/?EIO=4&transport=polling&t=PZfA6r6&sid=qN6dwdEW9UEskC94AAAA HTTP/1.1" 200 -
2025-08-27 09:33:17,517 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "GET /socket.io/?EIO=4&transport=polling&t=PZfA6r7&sid=qN6dwdEW9UEskC94AAAA HTTP/1.1" 200 -
2025-08-27 09:33:17,519 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "POST /socket.io/?EIO=4&transport=polling&t=PZfA6rA&sid=NjZDR_mly1wUFNzCAAAB HTTP/1.1" 200 -
2025-08-27 09:33:17,520 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "GET /socket.io/?EIO=4&transport=polling&t=PZfA6rC&sid=NjZDR_mly1wUFNzCAAAB HTTP/1.1" 200 -
2025-08-27 09:33:17,532 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "GET /socket.io/?EIO=4&transport=polling&t=PZfA6rI&sid=NjZDR_mly1wUFNzCAAAB HTTP/1.1" 200 -
2025-08-27 09:33:19,900 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:19] "GET / HTTP/1.1" 200 -
2025-08-27 09:33:19,993 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:19] "GET /static/css/主样式.css HTTP/1.1" 200 -
2025-08-27 09:33:19,993 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:19] "GET /static/js/主脚本.js HTTP/1.1" 200 -
2025-08-27 09:33:20,455 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /socket.io/?EIO=4&transport=polling&t=PZfA7Z5 HTTP/1.1" 200 -
2025-08-27 09:33:20,457 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:33:20,458 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:33:20,490 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "POST /socket.io/?EIO=4&transport=polling&t=PZfA7Ze&sid=gTMO1t4ZfJtNN3OFAAAE HTTP/1.1" 200 -
2025-08-27 09:33:20,492 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /socket.io/?EIO=4&transport=polling&t=PZfA7Zf&sid=gTMO1t4ZfJtNN3OFAAAE HTTP/1.1" 200 -
2025-08-27 09:33:20,527 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /socket.io/?EIO=4&transport=polling&t=PZfA7aC&sid=gTMO1t4ZfJtNN3OFAAAE HTTP/1.1" 200 -
2025-08-27 09:33:20,571 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /socket.io/?EIO=4&transport=polling&t=PZfA7au&sid=gTMO1t4ZfJtNN3OFAAAE HTTP/1.1" 200 -
2025-08-27 09:33:22,003 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:22] "GET /socket.io/?EIO=4&transport=websocket&sid=qN6dwdEW9UEskC94AAAA HTTP/1.1" 200 -
2025-08-27 09:33:33,293 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:33:33,324 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:33:33,333 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:33:33,501 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "GET /socket.io/?EIO=4&transport=polling&t=PZfAAkg HTTP/1.1" 200 -
2025-08-27 09:33:33,501 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:33:33,502 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "GET /socket.io/?EIO=4&transport=polling&t=PZfAAkf HTTP/1.1" 200 -
2025-08-27 09:33:33,568 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "POST /socket.io/?EIO=4&transport=polling&t=PZfAAl5&sid=2JTnSqnUcemBHRX3AAAG HTTP/1.1" 200 -
2025-08-27 09:33:33,569 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "GET /socket.io/?EIO=4&transport=websocket&sid=gTMO1t4ZfJtNN3OFAAAE HTTP/1.1" 200 -
2025-08-27 09:33:33,574 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "POST /socket.io/?EIO=4&transport=polling&t=PZfAAl6.0&sid=DtZv6w_e4brUAonHAAAH HTTP/1.1" 200 -
2025-08-27 09:33:33,576 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "GET /socket.io/?EIO=4&transport=polling&t=PZfAAl6&sid=2JTnSqnUcemBHRX3AAAG HTTP/1.1" 200 -
2025-08-27 09:33:33,581 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "GET /socket.io/?EIO=4&transport=polling&t=PZfAAl7&sid=DtZv6w_e4brUAonHAAAH HTTP/1.1" 200 -
2025-08-27 09:33:33,646 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:33] "GET /socket.io/?EIO=4&transport=polling&t=PZfAAmy&sid=DtZv6w_e4brUAonHAAAH HTTP/1.1" 200 -
2025-08-27 09:33:41,157 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:41] "GET /socket.io/?EIO=4&transport=polling&t=PZfACcZ HTTP/1.1" 200 -
2025-08-27 09:33:41,176 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:41] "GET /socket.io/?EIO=4&transport=websocket&sid=2JTnSqnUcemBHRX3AAAG HTTP/1.1" 200 -
2025-08-27 09:33:41,176 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:41] "GET /socket.io/?EIO=4&transport=websocket&sid=DtZv6w_e4brUAonHAAAH HTTP/1.1" 200 -
2025-08-27 09:33:41,187 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:41] "POST /socket.io/?EIO=4&transport=polling&t=PZfACd0&sid=qkFYDG4cz9SDHAVfAAAK HTTP/1.1" 200 -
2025-08-27 09:33:41,188 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:41] "GET /socket.io/?EIO=4&transport=polling&t=PZfACd1&sid=qkFYDG4cz9SDHAVfAAAK HTTP/1.1" 200 -
2025-08-27 09:33:41,206 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:41] "GET /socket.io/?EIO=4&transport=polling&t=PZfACdD&sid=qkFYDG4cz9SDHAVfAAAK HTTP/1.1" 200 -
2025-08-27 09:34:11,216 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:11] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:34:11,217 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:11] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:34:33,016 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:34:33,016 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:34:33,871 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:34:34,666 - WARNING -  * Debugger is active!
2025-08-27 09:34:34,669 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:34:35,214 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:35] "GET /socket.io/?EIO=4&transport=polling&t=PZfAPpB HTTP/1.1" 200 -
2025-08-27 09:34:35,219 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:35] "POST /socket.io/?EIO=4&transport=polling&t=PZfAPpF&sid=k62ha8bD-uLRdwHBAAAA HTTP/1.1" 200 -
2025-08-27 09:34:35,220 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:35] "GET /socket.io/?EIO=4&transport=polling&t=PZfAPpG&sid=k62ha8bD-uLRdwHBAAAA HTTP/1.1" 200 -
2025-08-27 09:34:36,208 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:36] "GET /socket.io/?EIO=4&transport=polling&t=PZfAQ2k HTTP/1.1" 200 -
2025-08-27 09:34:36,213 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:36] "POST /socket.io/?EIO=4&transport=polling&t=PZfAQ2o&sid=IrVw_qOwetibuqWVAAAC HTTP/1.1" 200 -
2025-08-27 09:34:36,214 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:36] "GET /socket.io/?EIO=4&transport=polling&t=PZfAQ2o.0&sid=IrVw_qOwetibuqWVAAAC HTTP/1.1" 200 -
2025-08-27 09:34:41,232 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:41] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:34:41,235 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:41] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:34:41,886 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:34:41,886 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:34:42,783 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:34:43,424 - WARNING -  * Debugger is active!
2025-08-27 09:34:43,427 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:34:44,221 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:44] "GET /socket.io/?EIO=4&transport=polling&t=PZfAR_w HTTP/1.1" 200 -
2025-08-27 09:34:44,223 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:44] "GET /socket.io/?EIO=4&transport=polling&t=PZfAR_w HTTP/1.1" 200 -
2025-08-27 09:34:44,227 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:44] "POST /socket.io/?EIO=4&transport=polling&t=PZfAR__&sid=-6hbuHhimWgWnx-5AAAA HTTP/1.1" 200 -
2025-08-27 09:34:44,228 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:44] "GET /socket.io/?EIO=4&transport=polling&t=PZfAR__.0&sid=-6hbuHhimWgWnx-5AAAA HTTP/1.1" 200 -
2025-08-27 09:34:44,229 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:44] "POST /socket.io/?EIO=4&transport=polling&t=PZfAS01&sid=Q9QDyssSh8wyBORJAAAB HTTP/1.1" 200 -
2025-08-27 09:34:44,230 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:44] "GET /socket.io/?EIO=4&transport=polling&t=PZfAS01.0&sid=Q9QDyssSh8wyBORJAAAB HTTP/1.1" 200 -
2025-08-27 09:34:44,252 - INFO - 127.0.0.1 - - [27/Aug/2025 09:34:44] "GET /socket.io/?EIO=4&transport=polling&t=PZfAS07&sid=Q9QDyssSh8wyBORJAAAB HTTP/1.1" 200 -
2025-08-27 09:35:11,209 - INFO - 127.0.0.1 - - [27/Aug/2025 09:35:11] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:35:11,209 - INFO - 127.0.0.1 - - [27/Aug/2025 09:35:11] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:35:41,220 - INFO - 127.0.0.1 - - [27/Aug/2025 09:35:41] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:35:41,221 - INFO - 127.0.0.1 - - [27/Aug/2025 09:35:41] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:36:11,216 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:11] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:36:11,217 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:11] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:36:12,544 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:36:12,544 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:36:12,852 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:36:13,551 - WARNING -  * Debugger is active!
2025-08-27 09:36:13,554 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:36:14,221 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:14] "GET /socket.io/?EIO=4&transport=polling&t=PZfAn-6 HTTP/1.1" 200 -
2025-08-27 09:36:14,235 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:14] "POST /socket.io/?EIO=4&transport=polling&t=PZfAn-G&sid=iKN7RaTbco2b-d2NAAAA HTTP/1.1" 200 -
2025-08-27 09:36:14,241 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:14] "GET /socket.io/?EIO=4&transport=polling&t=PZfAn-M&sid=iKN7RaTbco2b-d2NAAAA HTTP/1.1" 200 -
2025-08-27 09:36:15,294 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:15] "GET /socket.io/?EIO=4&transport=polling&t=PZfAoEL HTTP/1.1" 200 -
2025-08-27 09:36:15,320 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:15] "POST /socket.io/?EIO=4&transport=polling&t=PZfAoF7&sid=LYzZEZwCcmDcriRUAAAC HTTP/1.1" 200 -
2025-08-27 09:36:15,337 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:15] "GET /socket.io/?EIO=4&transport=polling&t=PZfAoF8&sid=LYzZEZwCcmDcriRUAAAC HTTP/1.1" 200 -
2025-08-27 09:36:15,371 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:15] "GET /socket.io/?EIO=4&transport=polling&t=PZfAoFu&sid=LYzZEZwCcmDcriRUAAAC HTTP/1.1" 200 -
2025-08-27 09:36:41,213 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:41] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:36:41,214 - INFO - 127.0.0.1 - - [27/Aug/2025 09:36:41] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:37:53,228 - INFO - 127.0.0.1 - - [27/Aug/2025 09:37:53] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:37:53,232 - INFO - 127.0.0.1 - - [27/Aug/2025 09:37:53] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:38:07,716 - INFO - 127.0.0.1 - - [27/Aug/2025 09:38:07] "GET /socket.io/?EIO=4&transport=websocket&sid=LYzZEZwCcmDcriRUAAAC HTTP/1.1" 200 -
2025-08-27 09:38:08,037 - INFO - 127.0.0.1 - - [27/Aug/2025 09:38:08] "GET /socket.io/?EIO=4&transport=websocket&sid=iKN7RaTbco2b-d2NAAAA HTTP/1.1" 200 -
2025-08-27 09:40:27,880 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:7811
2025-08-27 09:40:27,881 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:40:27,881 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:40:28,541 - WARNING -  * Debugger is active!
2025-08-27 09:40:28,545 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:40:29,579 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:29] "[32mGET / HTTP/1.1[0m" 302 -
2025-08-27 09:40:29,601 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:29] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:40:29,679 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:29] "GET /static/css/主样式.css HTTP/1.1" 200 -
2025-08-27 09:40:29,679 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:29] "GET /static/js/主脚本.js HTTP/1.1" 200 -
2025-08-27 09:40:30,121 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmSd HTTP/1.1" 200 -
2025-08-27 09:40:30,128 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmSh HTTP/1.1" 200 -
2025-08-27 09:40:30,129 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:40:30,179 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "POST /socket.io/?EIO=4&transport=polling&t=PZfBmTV&sid=KH5wzINU-Qn5P2q2AAAA HTTP/1.1" 200 -
2025-08-27 09:40:30,181 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "POST /socket.io/?EIO=4&transport=polling&t=PZfBmTW.0&sid=l44VTTkYHNdE8ZSCAAAB HTTP/1.1" 200 -
2025-08-27 09:40:30,181 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmTW&sid=KH5wzINU-Qn5P2q2AAAA HTTP/1.1" 200 -
2025-08-27 09:40:30,182 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmTW.1&sid=l44VTTkYHNdE8ZSCAAAB HTTP/1.1" 200 -
2025-08-27 09:40:30,235 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmUM&sid=KH5wzINU-Qn5P2q2AAAA HTTP/1.1" 200 -
2025-08-27 09:40:30,236 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmUM.0&sid=l44VTTkYHNdE8ZSCAAAB HTTP/1.1" 200 -
2025-08-27 09:40:30,284 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "[32mGET / HTTP/1.1[0m" 302 -
2025-08-27 09:40:30,288 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmVC&sid=l44VTTkYHNdE8ZSCAAAB HTTP/1.1" 200 -
2025-08-27 09:40:30,289 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-27 09:40:30,293 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:40:30,314 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:40:30,316 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:40:30,918 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PZfBme_ HTTP/1.1" 200 -
2025-08-27 09:40:30,922 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmf2 HTTP/1.1" 200 -
2025-08-27 09:40:30,930 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:40:30,980 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "POST /socket.io/?EIO=4&transport=polling&t=PZfBmg0&sid=7Q8TBC_PHasJxGr1AAAF HTTP/1.1" 200 -
2025-08-27 09:40:30,996 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "POST /socket.io/?EIO=4&transport=polling&t=PZfBmf_&sid=kBA3N0Bj5tG39TUBAAAE HTTP/1.1" 200 -
2025-08-27 09:40:30,996 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:30] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmg1&sid=7Q8TBC_PHasJxGr1AAAF HTTP/1.1" 200 -
2025-08-27 09:40:31,000 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:31] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmf_.0&sid=kBA3N0Bj5tG39TUBAAAE HTTP/1.1" 200 -
2025-08-27 09:40:31,060 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:31] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmhD&sid=7Q8TBC_PHasJxGr1AAAF HTTP/1.1" 200 -
2025-08-27 09:40:31,061 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:31] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmhG&sid=kBA3N0Bj5tG39TUBAAAE HTTP/1.1" 200 -
2025-08-27 09:40:31,111 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:31] "GET /socket.io/?EIO=4&transport=polling&t=PZfBmi1&sid=7Q8TBC_PHasJxGr1AAAF HTTP/1.1" 200 -
2025-08-27 09:40:57,915 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:7811
2025-08-27 09:40:57,915 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:40:57,916 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:40:58,590 - WARNING -  * Debugger is active!
2025-08-27 09:40:58,594 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:40:58,605 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:58] "GET /socket.io/?EIO=4&transport=polling&t=PZfBspJ HTTP/1.1" 200 -
2025-08-27 09:40:58,610 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:58] "POST /socket.io/?EIO=4&transport=polling&t=PZfBtPl&sid=vso9eC-afsh8n2-DAAAA HTTP/1.1" 200 -
2025-08-27 09:40:58,611 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:58] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtPm&sid=vso9eC-afsh8n2-DAAAA HTTP/1.1" 200 -
2025-08-27 09:40:58,617 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:58] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtPs&sid=vso9eC-afsh8n2-DAAAA HTTP/1.1" 200 -
2025-08-27 09:40:59,643 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:59] "[32mGET / HTTP/1.1[0m" 302 -
2025-08-27 09:40:59,671 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:59] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:40:59,752 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:59] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:40:59,753 - INFO - 127.0.0.1 - - [27/Aug/2025 09:40:59] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:41:00,176 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtoE HTTP/1.1" 200 -
2025-08-27 09:41:00,180 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtoG HTTP/1.1" 200 -
2025-08-27 09:41:00,181 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:41:00,210 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtol HTTP/1.1" 200 -
2025-08-27 09:41:00,215 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "POST /socket.io/?EIO=4&transport=polling&t=PZfBtoq&sid=1nwVBm_3EIXggZfRAAAE HTTP/1.1" 200 -
2025-08-27 09:41:00,217 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtor&sid=1nwVBm_3EIXggZfRAAAE HTTP/1.1" 200 -
2025-08-27 09:41:00,236 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "POST /socket.io/?EIO=4&transport=polling&t=PZfBtp8&sid=FrSA7cxopiMajez6AAAC HTTP/1.1" 200 -
2025-08-27 09:41:00,237 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtp8.0&sid=FrSA7cxopiMajez6AAAC HTTP/1.1" 200 -
2025-08-27 09:41:00,238 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "POST /socket.io/?EIO=4&transport=polling&t=PZfBtp9&sid=DxxEYPoxzd6PsQn_AAAD HTTP/1.1" 200 -
2025-08-27 09:41:00,239 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtp9.0&sid=DxxEYPoxzd6PsQn_AAAD HTTP/1.1" 200 -
2025-08-27 09:41:00,338 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtq4&sid=FrSA7cxopiMajez6AAAC HTTP/1.1" 200 -
2025-08-27 09:41:00,342 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "[32mGET / HTTP/1.1[0m" 302 -
2025-08-27 09:41:00,344 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtq4.0&sid=DxxEYPoxzd6PsQn_AAAD HTTP/1.1" 200 -
2025-08-27 09:41:00,350 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:41:00,392 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:41:00,392 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:41:00,489 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtt7 HTTP/1.1" 200 -
2025-08-27 09:41:00,516 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "POST /socket.io/?EIO=4&transport=polling&t=PZfBttQ&sid=Lp3t3jcrDNmiePVSAAAI HTTP/1.1" 200 -
2025-08-27 09:41:00,517 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBttR&sid=Lp3t3jcrDNmiePVSAAAI HTTP/1.1" 200 -
2025-08-27 09:41:00,881 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtzF HTTP/1.1" 200 -
2025-08-27 09:41:00,885 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBtzI HTTP/1.1" 200 -
2025-08-27 09:41:00,886 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:41:00,935 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "POST /socket.io/?EIO=4&transport=polling&t=PZfBt-3&sid=LzXl4XLJlu3wwvozAAAK HTTP/1.1" 200 -
2025-08-27 09:41:00,936 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBt-3.0&sid=LzXl4XLJlu3wwvozAAAK HTTP/1.1" 200 -
2025-08-27 09:41:00,937 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "POST /socket.io/?EIO=4&transport=polling&t=PZfBt-4&sid=Sr6RNtpbPqUrWiEPAAAL HTTP/1.1" 200 -
2025-08-27 09:41:00,940 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBt-4.0&sid=Sr6RNtpbPqUrWiEPAAAL HTTP/1.1" 200 -
2025-08-27 09:41:00,995 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBt-v&sid=LzXl4XLJlu3wwvozAAAK HTTP/1.1" 200 -
2025-08-27 09:41:00,996 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:00] "GET /socket.io/?EIO=4&transport=polling&t=PZfBt-w&sid=Sr6RNtpbPqUrWiEPAAAL HTTP/1.1" 200 -
2025-08-27 09:41:01,048 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:01] "GET /socket.io/?EIO=4&transport=polling&t=PZfBt_p&sid=LzXl4XLJlu3wwvozAAAK HTTP/1.1" 200 -
2025-08-27 09:41:01,049 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:01] "GET /socket.io/?EIO=4&transport=polling&t=PZfBt_p.0&sid=Sr6RNtpbPqUrWiEPAAAL HTTP/1.1" 200 -
2025-08-27 09:41:01,284 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:01] "GET /socket.io/?EIO=4&transport=polling&t=PZfBu39 HTTP/1.1" 200 -
2025-08-27 09:41:01,304 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:01] "POST /socket.io/?EIO=4&transport=polling&t=PZfBu3g&sid=ZY0s9bAR0If8hPyVAAAO HTTP/1.1" 200 -
2025-08-27 09:41:01,392 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:01] "GET /socket.io/?EIO=4&transport=polling&t=PZfBu3g.0&sid=ZY0s9bAR0If8hPyVAAAO HTTP/1.1" 200 -
2025-08-27 09:41:01,427 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:01] "GET /socket.io/?EIO=4&transport=websocket&sid=LzXl4XLJlu3wwvozAAAK HTTP/1.1" 200 -
2025-08-27 09:41:01,461 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:01] "GET /socket.io/?EIO=4&transport=websocket&sid=Sr6RNtpbPqUrWiEPAAAL HTTP/1.1" 200 -
2025-08-27 09:41:01,698 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:01] "GET /socket.io/?EIO=4&transport=websocket&sid=DxxEYPoxzd6PsQn_AAAD HTTP/1.1" 200 -
2025-08-27 09:41:01,698 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:01] "GET /socket.io/?EIO=4&transport=websocket&sid=FrSA7cxopiMajez6AAAC HTTP/1.1" 200 -
2025-08-27 09:41:02,327 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:02] "GET /socket.io/?EIO=4&transport=websocket&sid=ZY0s9bAR0If8hPyVAAAO HTTP/1.1" 200 -
2025-08-27 09:41:02,329 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:02] "GET /socket.io/?EIO=4&transport=websocket&sid=1nwVBm_3EIXggZfRAAAE HTTP/1.1" 200 -
2025-08-27 09:41:02,693 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:02] "GET /socket.io/?EIO=4&transport=websocket&sid=vso9eC-afsh8n2-DAAAA HTTP/1.1" 200 -
2025-08-27 09:41:02,693 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:02] "GET /socket.io/?EIO=4&transport=websocket&sid=Lp3t3jcrDNmiePVSAAAI HTTP/1.1" 200 -
2025-08-27 09:41:36,839 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:7811
2025-08-27 09:41:36,839 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:41:38,560 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:38] "[32mGET / HTTP/1.1[0m" 302 -
2025-08-27 09:41:38,581 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:38] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:41:38,666 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:38] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:41:38,667 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:38] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:41:39,063 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "GET /socket.io/?EIO=4&transport=polling&t=PZfC1Hr HTTP/1.1" 200 -
2025-08-27 09:41:39,066 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "GET /socket.io/?EIO=4&transport=polling&t=PZfC1Ht HTTP/1.1" 200 -
2025-08-27 09:41:39,068 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:41:39,113 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "POST /socket.io/?EIO=4&transport=polling&t=PZfC1Ia&sid=Y-KUlJPVJvolZzi-AAAA HTTP/1.1" 200 -
2025-08-27 09:41:39,113 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "GET /socket.io/?EIO=4&transport=polling&t=PZfC1Ib&sid=Y-KUlJPVJvolZzi-AAAA HTTP/1.1" 200 -
2025-08-27 09:41:39,115 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "POST /socket.io/?EIO=4&transport=polling&t=PZfC1Ic&sid=Xppxc7WAKfFyD1KdAAAB HTTP/1.1" 200 -
2025-08-27 09:41:39,115 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "GET /socket.io/?EIO=4&transport=polling&t=PZfC1Ic.0&sid=Xppxc7WAKfFyD1KdAAAB HTTP/1.1" 200 -
2025-08-27 09:41:39,157 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "GET /socket.io/?EIO=4&transport=polling&t=PZfC1JG&sid=Y-KUlJPVJvolZzi-AAAA HTTP/1.1" 200 -
2025-08-27 09:41:39,161 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "GET /socket.io/?EIO=4&transport=polling&t=PZfC1JG.0&sid=Xppxc7WAKfFyD1KdAAAB HTTP/1.1" 200 -
2025-08-27 09:41:39,197 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "GET /socket.io/?EIO=4&transport=polling&t=PZfC1Jv&sid=Y-KUlJPVJvolZzi-AAAA HTTP/1.1" 200 -
2025-08-27 09:41:39,198 - INFO - 127.0.0.1 - - [27/Aug/2025 09:41:39] "GET /socket.io/?EIO=4&transport=polling&t=PZfC1Jw&sid=Xppxc7WAKfFyD1KdAAAB HTTP/1.1" 200 -
2025-08-27 09:47:02,536 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:7811
2025-08-27 09:47:02,536 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:47:02,538 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:47:03,947 - WARNING -  * Debugger is active!
2025-08-27 09:47:03,951 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:47:04,249 - INFO - 127.0.0.1 - - [27/Aug/2025 09:47:04] "[32mGET / HTTP/1.1[0m" 302 -
2025-08-27 09:47:04,300 - INFO - 127.0.0.1 - - [27/Aug/2025 09:47:04] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:47:04,444 - INFO - 127.0.0.1 - - [27/Aug/2025 09:47:04] "GET /static/js/主脚本.js HTTP/1.1" 200 -
2025-08-27 09:47:04,453 - INFO - 127.0.0.1 - - [27/Aug/2025 09:47:04] "GET /static/css/主样式.css HTTP/1.1" 200 -
