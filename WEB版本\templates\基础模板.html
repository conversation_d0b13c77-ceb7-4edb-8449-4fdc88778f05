<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>{% block title %}闲鱼商品采集工具 - WEB版本{% endblock %}</title>
  
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <!-- Font Awesome -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
  <!-- 自定义CSS -->
  <link href="{{ url_for('static', filename='css/主样式.css') }}" rel="stylesheet" />
  
  {% block extra_css %}{% endblock %}
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
      <a class="navbar-brand" href="{{ url_for('数据采集页面') }}">
        <i class="fas fa-shopping-cart me-2"></i>
        闲鱼采集工具
      </a>
      
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('数据采集页面') }}">
              <i class="fas fa-download me-1"></i>数据采集
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('数据查看页面') }}">
              <i class="fas fa-table me-1"></i>数据查看
            </a>
          </li>

          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('系统设置页面') }}">
              <i class="fas fa-cog me-1"></i>系统设置
            </a>
          </li>
        </ul>
        
        <!-- 状态指示器 -->
        <div class="navbar-text me-3">
          <span id="status-indicator" class="badge bg-success">
            <i class="fas fa-circle me-1"></i>空闲
          </span>
        </div>
      </div>
    </div>
  </nav>

  <!-- 主要内容区域 -->
  <main class="container-fluid py-4">
    {% block content %}{% endblock %}
  </main>

  <!-- 实时消息显示区域 -->
  <div id="message-container" class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
    <!-- 消息将通过JavaScript动态添加 -->
  </div>

  <!-- 页脚 -->
  <footer class="bg-light text-center py-3 mt-5">
    <div class="container">
      <p class="text-muted mb-0">
        <i class="fas fa-code me-1"></i>
        闲鱼商品采集工具 WEB版本 - 基于Flask开发
      </p>
    </div>
  </footer>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Socket.IO -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <!-- 自定义JS -->
  <script src="{{ url_for('static', filename='js/主脚本.js') }}"></script>
  
  {% block extra_js %}{% endblock %}
</body>
</html>
